import { v4 as uuidv4 } from 'uuid';
import { AgentType } from '../../types.js';
import { BackendGeminiService } from './BackendGeminiService.js';
import { BackendPlannerAgent } from '../agents/BackendPlannerAgent.js';
import { AudioGeneratorAgent } from '../agents/AudioGeneratorAgent.js';
import { AssetManagerAgent } from '../agents/AssetManagerAgent.js';
import { DeploymentManagerAgent } from '../agents/DeploymentManagerAgent.js';
import { DependencyResolverAgent } from '../agents/DependencyResolverAgent.js';
import { QualityAssuranceAgent } from '../agents/QualityAssuranceAgent.js';
import { GameDesignerAgent } from '../agents/GameDesignerAgent.js';
/**
 * Service that handles the complete project generation workflow on the backend
 * This replaces the frontend logic that was causing memory issues
 */
export class ProjectGenerationService {
    constructor(io) {
        this.geminiService = null;
        this.plannerAgent = null;
        this.audioGeneratorAgent = null;
        this.assetManagerAgent = null;
        this.deploymentManagerAgent = null;
        this.dependencyResolverAgent = null;
        this.qualityAssuranceAgent = null;
        this.gameDesignerAgent = null;
        this.isGenerating = false;
        this.currentProjectId = null;
        this.io = io;
    }
    /**
     * Initialize the service with API key and create agents
     */
    initializeWithApiKey(apiKey) {
        this.geminiService = new BackendGeminiService(apiKey);
        const loggingInterface = {
            addCompanyLog: this.addCompanyLog.bind(this),
            addTaskLog: (taskId, agent, message, status, stage, subDetailSections) => {
                this.addTaskLog(taskId, agent, message, status, stage || 'QUEUED', subDetailSections);
            },
            addDecisionLogEntry: (agent, action, details, reason, taskId) => {
                this.addDecisionLogEntry(agent, action, details, reason, taskId);
            }
        };
        // Initialize all agents
        this.plannerAgent = new BackendPlannerAgent(this.geminiService, loggingInterface);
        this.audioGeneratorAgent = new AudioGeneratorAgent(this.geminiService, loggingInterface);
        this.assetManagerAgent = new AssetManagerAgent(this.geminiService, loggingInterface);
        this.deploymentManagerAgent = new DeploymentManagerAgent(this.geminiService, loggingInterface);
        this.dependencyResolverAgent = new DependencyResolverAgent(this.geminiService, loggingInterface);
        this.qualityAssuranceAgent = new QualityAssuranceAgent(this.geminiService, loggingInterface);
        this.gameDesignerAgent = new GameDesignerAgent(this.geminiService, loggingInterface);
    }
    /**
     * Generate a complete project with full autonomy using all specialized agents
     */
    async generateCompleteAutonomousProject(idea, userAuthorshipDetails, apiKey) {
        if (this.isGenerating) {
            throw new Error('Project generation already in progress');
        }
        this.isGenerating = true;
        this.currentProjectId = uuidv4();
        try {
            this.initializeWithApiKey(apiKey);
            if (!this.plannerAgent || !this.dependencyResolverAgent || !this.assetManagerAgent ||
                !this.audioGeneratorAgent || !this.deploymentManagerAgent || !this.qualityAssuranceAgent) {
                throw new Error('Required agents not initialized');
            }
            await this.addCompanyLog('Project Manager', 'Starting complete autonomous project generation...', 'working');
            // Phase 1: Initial Planning (Enhanced for games if applicable)
            let plannerResponse;
            if (this.isGameProject(idea) && this.gameDesignerAgent) {
                await this.addCompanyLog('Project Manager', 'Detected game project - using specialized game design...', 'info');
                plannerResponse = await this.gameDesignerAgent.designGameSystems({ idea, fullContext: idea, name: '', tasks: [], fileStructure: [] }, 'gemini-2.5-flash-preview-04-17');
            }
            else {
                plannerResponse = await this.plannerAgent.generatePlan(idea, 'gemini-2.5-flash-preview-04-17', idea, { type: 'MIT', details: 'Open source project' });
            }
            // Create initial project structure
            const project = {
                id: this.currentProjectId,
                name: plannerResponse.projectName || this.generateProjectName(idea),
                idea: idea,
                tasks: plannerResponse.tasks,
                fileStructure: plannerResponse.fileStructure || [],
                technologyStack: plannerResponse.technologyStackSuggestion,
                userAuthorshipDetails: userAuthorshipDetails,
                createdAt: new Date(),
                lastModified: new Date(),
                status: 'in-progress'
            };
            // Emit project created event
            this.io.emit('projectCreated', project);
            await this.addCompanyLog('Project Manager', `Project "${project.name}" created with ${project.tasks.length} tasks`, 'success');
            // Phase 2: Dependency Resolution
            await this.addCompanyLog('Project Manager', 'Resolving project dependencies...', 'working');
            const projectContext = this.createProjectContext(project);
            const dependencyResponse = await this.dependencyResolverAgent.resolveDependencies(projectContext, 'gemini-2.5-flash-preview-04-17');
            // Phase 3: Asset Management
            await this.addCompanyLog('Project Manager', 'Managing project assets...', 'working');
            const assetResponse = await this.assetManagerAgent.manageProjectAssets(projectContext, 'gemini-2.5-flash-preview-04-17');
            // Phase 4: Audio Generation (if needed)
            const audioRequirements = this.extractAudioRequirements(project);
            if (audioRequirements.length > 0) {
                await this.addCompanyLog('Project Manager', 'Generating audio assets...', 'working');
                const audioResponse = await this.audioGeneratorAgent.generateAudioAssets(projectContext, audioRequirements, 'gemini-2.5-flash-preview-04-17');
            }
            // Phase 5: Deployment Configuration
            await this.addCompanyLog('Project Manager', 'Configuring deployment...', 'working');
            const deploymentResponse = await this.deploymentManagerAgent.deployProject(projectContext, 'gemini-2.5-flash-preview-04-17');
            // Phase 6: Quality Assurance
            await this.addCompanyLog('Project Manager', 'Performing quality assurance...', 'working');
            const qaResponse = await this.qualityAssuranceAgent.performQualityAssurance(projectContext, 'gemini-2.5-flash-preview-04-17');
            // Update project with all enhancements
            project.lastModified = new Date();
            this.io.emit('projectUpdated', project);
            await this.addCompanyLog('Project Manager', `Complete autonomous project generation finished! Quality Score: ${qaResponse.overallScore}/100`, qaResponse.readinessStatus === 'ready' ? 'success' : 'info');
        }
        catch (error) {
            this.isGenerating = false;
            this.currentProjectId = null;
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.addCompanyLog('Project Manager', `Error generating project: ${errorMessage}`, 'error');
            throw error;
        }
        finally {
            this.isGenerating = false;
            this.currentProjectId = null;
        }
    }
    /**
     * Start project generation workflow
     */
    async startProjectGeneration(projectId, projectIdea, licenseInfo, agentModelConfiguration) {
        if (this.isGenerating) {
            throw new Error('Project generation already in progress');
        }
        if (!this.geminiService || !this.plannerAgent) {
            throw new Error('Service not initialized with API key');
        }
        this.isGenerating = true;
        this.currentProjectId = projectId;
        try {
            await this.addCompanyLog('System', `Starting project generation for: ${projectIdea}`, 'working');
            // Phase 1: Generate initial plan
            await this.addCompanyLog('Planner Agent', 'Generating initial project plan...', 'working');
            const plannerModel = agentModelConfiguration[AgentType.PLANNER];
            const projectContext = `Project ID: ${projectId}\nProject Idea: ${projectIdea}\nLicense: ${licenseInfo.type}`;
            const initialPlan = await this.plannerAgent.generatePlan(projectIdea, plannerModel, projectContext, licenseInfo);
            await this.addCompanyLog('Planner Agent', `Initial plan generated with ${initialPlan.tasks.length} tasks`, 'success');
            await this.addDecisionLogEntry('Planner Agent', 'Initial Plan Generated', `Created ${initialPlan.tasks.length} tasks and ${initialPlan.fileStructure?.length || 0} file structure items`);
            // Emit initial plan to frontend
            this.io.emit('project-generation-update', {
                projectId,
                phase: 'planning',
                data: {
                    tasks: initialPlan.tasks,
                    fileStructure: initialPlan.fileStructure,
                    technologyStackSuggestion: initialPlan.technologyStackSuggestion
                }
            });
            // Phase 2: Review and refine plan
            await this.addCompanyLog('Planner Agent', 'Reviewing and refining initial plan...', 'working');
            const refinedPlan = await this.plannerAgent.reviewAndRefinePlan(projectIdea, initialPlan.tasks, initialPlan.fileStructure || [], projectContext, plannerModel, initialPlan.technologyStackSuggestion);
            await this.addCompanyLog('Planner Agent', 'Plan reviewed and finalized', 'success');
            await this.addDecisionLogEntry('Planner Agent', 'Plan Reviewed & Finalized', `Final plan: ${refinedPlan.tasks.length} tasks, ${refinedPlan.fileStructure?.length || 0} files`);
            // Convert to full Task objects
            const finalTasks = refinedPlan.tasks.map((t) => ({
                ...t,
                id: t.id || uuidv4(),
                status: 'pending',
                currentProcessingStage: 'QUEUED',
                agentMessages: [],
                identifiedBugs: [],
                unresolvedBugs: [],
                bugFixingCycles: 0
            }));
            // Convert to full FileNode objects
            const finalFileStructure = (refinedPlan.fileStructure || []).map((n) => ({
                ...n,
                id: uuidv4(),
                path: n.name,
                children: n.children?.map((c) => ({
                    ...c,
                    id: uuidv4(),
                    path: `${n.name}/${c.name}`,
                    children: []
                })) || []
            }));
            // Emit final plan to frontend
            this.io.emit('project-generation-update', {
                projectId,
                phase: 'plan-finalized',
                data: {
                    tasks: finalTasks,
                    fileStructure: finalFileStructure,
                    technologyStackSuggestion: refinedPlan.technologyStackSuggestion,
                    reviewNotes: refinedPlan.reviewNotes
                }
            });
            await this.addCompanyLog('System', 'Project generation completed successfully', 'success');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.addCompanyLog('System', `Project generation failed: ${errorMessage}`, 'error');
            this.io.emit('project-generation-error', {
                projectId,
                error: errorMessage
            });
            throw error;
        }
        finally {
            this.isGenerating = false;
            this.currentProjectId = null;
        }
    }
    /**
     * Process user feedback and generate additional tasks
     */
    async processUserFeedback(_projectId, projectContext, projectIdea, fileStructure, feedback, modelName) {
        if (!this.plannerAgent) {
            throw new Error('Service not initialized');
        }
        await this.addCompanyLog('Planner Agent', `Processing user feedback: ${feedback.description.substring(0, 50)}...`, 'working');
        const feedbackResponse = await this.plannerAgent.generateTasksFromFeedback(projectContext, projectIdea, fileStructure, feedback, modelName);
        const newTasks = feedbackResponse.tasks.map((t) => ({
            ...t,
            id: t.id || uuidv4(),
            status: 'pending',
            currentProcessingStage: 'QUEUED',
            agentMessages: [],
            identifiedBugs: [],
            unresolvedBugs: [],
            bugFixingCycles: 0,
            purpose: 'user-feedback-driven'
        }));
        await this.addCompanyLog('Planner Agent', `Generated ${newTasks.length} tasks from user feedback`, 'success');
        await this.addDecisionLogEntry('Planner Agent', 'Tasks Generated from User Feedback', `Generated ${newTasks.length} tasks to address user feedback`);
        return newTasks;
    }
    /**
     * Get current generation status
     */
    getGenerationStatus() {
        return {
            isGenerating: this.isGenerating,
            projectId: this.currentProjectId
        };
    }
    /**
     * Add company log and emit to frontend
     */
    async addCompanyLog(agent, message, status, _taskId) {
        const logEntry = {
            id: uuidv4(),
            timestamp: new Date(),
            agent,
            message,
            status
        };
        this.io.emit('company-log', logEntry);
        console.log(`[${agent}] ${message}`);
    }
    /**
     * Add task log and emit to frontend
     */
    async addTaskLog(taskId, agent, message, status, stage, attachments) {
        const logEntry = {
            taskId,
            agent,
            message,
            status,
            stage,
            attachments,
            timestamp: new Date()
        };
        this.io.emit('task-log', logEntry);
        console.log(`[${agent}] Task ${taskId}: ${message}`);
    }
    /**
     * Add decision log entry and emit to frontend
     */
    async addDecisionLogEntry(agent, decision, reasoning, stackTrace, taskId) {
        // Map string agent names to proper types
        let agentType;
        switch (agent) {
            case 'Planner Agent':
                agentType = AgentType.PLANNER;
                break;
            case 'Coder Agent':
                agentType = AgentType.CODER;
                break;
            case 'Context Manager Agent':
                agentType = AgentType.CONTEXT_MANAGER;
                break;
            case 'System':
                agentType = 'System';
                break;
            default:
                agentType = 'System'; // Default fallback
        }
        const entry = {
            id: uuidv4(),
            timestamp: new Date(),
            agent: agentType,
            action: decision,
            details: reasoning,
            reason: stackTrace,
            taskId
        };
        this.io.emit('decision-log', entry);
        console.log(`[${agent}] Decision: ${decision} - ${reasoning}`);
    }
    /**
     * Helper methods for autonomous project generation
     */
    isGameProject(idea) {
        const gameKeywords = ['game', 'rpg', 'tower', 'platformer', 'puzzle', 'strategy', 'shooter', 'racing', 'card', 'simulation'];
        const lowerIdea = idea.toLowerCase();
        return gameKeywords.some(keyword => lowerIdea.includes(keyword));
    }
    createProjectContext(project) {
        return {
            idea: project.idea,
            fullContext: `Project: ${project.name}\nIdea: ${project.idea}\nTechnology Stack: ${project.technologyStack}`,
            name: project.name,
            tasks: project.tasks,
            fileStructure: project.fileStructure,
            suggestedTechnologyStack: project.technologyStack
        };
    }
    generateProjectName(idea) {
        // Simple project name generation from idea
        const words = idea.split(' ').slice(0, 3);
        return words.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
    extractAudioRequirements(project) {
        const requirements = [];
        const idea = project.idea.toLowerCase();
        if (this.isGameProject(idea)) {
            requirements.push('Background music for gameplay');
            requirements.push('Sound effects for user interactions');
            requirements.push('Ambient sounds for atmosphere');
            if (idea.includes('rpg')) {
                requirements.push('Battle music');
                requirements.push('Victory fanfare');
                requirements.push('Menu navigation sounds');
            }
        }
        else if (idea.includes('app') || idea.includes('website')) {
            requirements.push('UI interaction sounds');
            requirements.push('Notification sounds');
        }
        return requirements;
    }
}
//# sourceMappingURL=ProjectGenerationService.js.map