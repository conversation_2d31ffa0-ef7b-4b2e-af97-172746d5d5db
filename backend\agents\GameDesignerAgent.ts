import { 
  LoggingInterface,
  ProjectContext,
  GeminiJsonPlannerResponse 
} from '../../types.js';
import { BackendGeminiService } from '../services/BackendGeminiService.js';

/**
 * Game Designer Agent - Specialized agent for game development
 * Handles game mechanics, balance, progression systems, and game-specific features
 */
export class GameDesignerAgent {
  constructor(
    private geminiService: BackendGeminiService,
    private loggingInterface: LoggingInterface
  ) {}

  /**
   * Log activity to the frontend
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working'): Promise<void> {
    await this.loggingInterface.addCompanyLog('Game Designer Agent', message, status);
  }

  /**
   * Log decision to the frontend
   */
  private logDecision(decision: string, reasoning: string, stackTrace?: string, taskId?: string): void {
    this.loggingInterface.addDecisionLogEntry('GAME_DESIGNER', decision, reasoning, stackTrace, taskId);
  }

  /**
   * Design comprehensive game systems and mechanics
   */
  public async designGameSystems(
    projectContext: ProjectContext,
    modelName: string,
    taskId?: string
  ): Promise<GeminiJsonPlannerResponse> {
    await this.logActivity('Designing game systems and mechanics...', 'working');
    
    try {
      const gameType = this.identifyGameType(projectContext);
      const prompt = this.buildGameDesignPrompt(projectContext, gameType);
      
      const response = await this.geminiService.makeRequestWithRetry<GeminiJsonPlannerResponse>(
        modelName,
        prompt,
        this.getGameDesignSystemInstruction(),
        this.validateGameDesignResponse.bind(this),
        0.7
      );

      await this.logActivity(`Designed ${response.tasks.length} game system tasks for ${gameType}`, 'success');
      this.logDecision(
        'Game Systems Designed',
        `Created comprehensive game design for ${gameType} including mechanics, progression, and balance systems`,
        undefined,
        taskId
      );

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Error designing game systems: ${errorMessage}`, 'error');
      throw error;
    }
  }

  /**
   * Generate game balance and progression systems
   */
  public async generateGameBalance(
    projectContext: ProjectContext,
    gameType: string,
    modelName: string
  ): Promise<string> {
    const prompt = `
Project: ${projectContext.idea}
Game Type: ${gameType}

Generate a comprehensive game balance document including:

1. **Player Progression System**
   - Level progression curves
   - Experience point calculations
   - Stat growth formulas
   - Unlock requirements

2. **Combat Balance**
   - Damage calculations
   - Health/defense scaling
   - Elemental effectiveness charts
   - Status effect durations

3. **Economy Balance**
   - Item pricing formulas
   - Resource generation rates
   - Shop inventory rotation
   - Currency exchange rates

4. **Difficulty Scaling**
   - Enemy stat progression
   - Challenge rating calculations
   - Boss encounter scaling
   - Adaptive difficulty systems

Provide specific formulas, tables, and implementation guidelines.
`;

    const response = await this.geminiService.makeRequestWithRetry<{balanceDocument: string}>(
      modelName,
      prompt,
      'You are a Game Balance Designer. Create detailed balance systems with specific formulas and values.',
      (data: any) => typeof data === 'object' && 'balanceDocument' in data,
      0.5
    );

    return response.balanceDocument;
  }

  /**
   * Identify the type of game being developed
   */
  private identifyGameType(projectContext: ProjectContext): string {
    const idea = projectContext.idea.toLowerCase();
    const context = projectContext.fullContext.toLowerCase();
    
    if (idea.includes('rpg') || idea.includes('role-playing')) {
      if (idea.includes('tower') || idea.includes('climbing')) return '2D RPG Tower Climber';
      return '2D RPG';
    }
    if (idea.includes('platformer') || idea.includes('platform')) return '2D Platformer';
    if (idea.includes('puzzle')) return 'Puzzle Game';
    if (idea.includes('strategy')) return 'Strategy Game';
    if (idea.includes('shooter')) return 'Shooter Game';
    if (idea.includes('racing')) return 'Racing Game';
    if (idea.includes('card') || idea.includes('deck')) return 'Card Game';
    if (idea.includes('simulation') || idea.includes('sim')) return 'Simulation Game';
    
    return 'Web Game';
  }

  /**
   * Build game design prompt
   */
  private buildGameDesignPrompt(projectContext: ProjectContext, gameType: string): string {
    return `
Project Idea: ${projectContext.idea}
Game Type: ${gameType}
Project Context: ${projectContext.fullContext}

Design comprehensive game systems for this ${gameType}. Create detailed tasks for:

**Core Game Systems:**
1. **Game Engine Setup** - Canvas/WebGL rendering, game loop, input handling
2. **Player Character System** - Stats, abilities, progression, customization
3. **Combat System** - Turn-based/real-time mechanics, damage calculation, status effects
4. **Inventory System** - Item management, equipment, consumables, storage
5. **Progression System** - Experience, leveling, skill trees, unlocks
6. **Save/Load System** - Game state persistence, multiple save slots

**Game-Specific Features:**
${this.getGameSpecificFeatures(gameType)}

**Audio-Visual Systems:**
7. **Graphics Engine** - Sprite rendering, animations, particle effects, UI
8. **Audio System** - Music management, sound effects, audio mixing
9. **User Interface** - Menus, HUD, inventory screens, settings

**Game Content:**
10. **Level Design** - Map generation, enemy placement, item distribution
11. **Content Management** - Asset loading, scene transitions, data management
12. **Game Balance** - Difficulty curves, reward systems, pacing

**Quality & Polish:**
13. **Performance Optimization** - Frame rate management, memory optimization
14. **Testing & Debug** - Debug tools, automated testing, quality assurance
15. **Deployment** - Build process, platform compatibility, distribution

Each task should be specific, implementable, and include technical details for the development team.
`;
  }

  /**
   * Get game-specific features based on game type
   */
  private getGameSpecificFeatures(gameType: string): string {
    switch (gameType) {
      case '2D RPG Tower Climber':
        return `
- **Tower Generation System** - Procedural floor generation, themed environments
- **Monster Collection** - Creature capture mechanics, breeding system, evolution
- **Elemental System** - Type advantages, elemental affinities, combo attacks
- **Boss Battle System** - Unique boss mechanics, multi-phase encounters
- **PvP Arena** - Player vs player battles, ranking system, tournaments
- **Breeding Algorithm** - Genetic traits, inheritance patterns, mutation system`;

      case '2D RPG':
        return `
- **Quest System** - Main story, side quests, dialogue trees
- **Party Management** - Multiple characters, formation system, AI companions
- **Magic System** - Spell casting, mana management, spell combinations
- **Crafting System** - Item creation, resource gathering, recipe discovery`;

      case '2D Platformer':
        return `
- **Physics Engine** - Collision detection, gravity, momentum
- **Level Editor** - User-generated content, sharing system
- **Power-up System** - Temporary abilities, collectible upgrades
- **Checkpoint System** - Save points, respawn mechanics`;

      case 'Puzzle Game':
        return `
- **Puzzle Engine** - Rule validation, solution checking, hint system
- **Level Progression** - Difficulty scaling, unlock system
- **Scoring System** - Points, time bonuses, combo multipliers
- **Hint System** - Progressive hints, solution replay`;

      default:
        return `
- **Game Loop** - Update/render cycle, state management
- **Input System** - Keyboard/mouse/touch handling, control mapping
- **Scene Management** - Game states, transitions, loading screens`;
    }
  }

  /**
   * Validate game design response
   */
  private validateGameDesignResponse(data: any): data is GeminiJsonPlannerResponse {
    return (
      typeof data === 'object' &&
      data !== null &&
      'tasks' in data &&
      Array.isArray(data.tasks) &&
      data.tasks.length > 0 &&
      data.tasks.every((task: any) =>
        typeof task === 'object' &&
        'description' in task &&
        typeof task.description === 'string'
      )
    );
  }

  /**
   * Get system instruction for game design
   */
  private getGameDesignSystemInstruction(): string {
    return `You are a Game Designer Agent specialized in creating comprehensive game development plans.

Your expertise includes:
- Game mechanics design and balance
- Player progression systems
- Combat and interaction systems
- User experience and game flow
- Technical implementation planning
- Performance optimization for games
- Cross-platform compatibility

When designing game systems:

1. **Create Granular Tasks**: Break down complex systems into implementable tasks
2. **Consider Technical Constraints**: Web-based games, performance limitations, browser compatibility
3. **Plan for Scalability**: Systems that can grow and be extended
4. **Include Game Balance**: Progression curves, difficulty scaling, reward systems
5. **Focus on Player Experience**: Intuitive controls, clear feedback, engaging mechanics
6. **Plan Asset Requirements**: Graphics, audio, data files needed
7. **Consider Accessibility**: Controls, visual/audio accessibility features

For each task, provide:
- Clear, specific description
- Technical implementation details
- File path where code should be implemented
- Dependencies on other tasks
- Estimated complexity
- Priority level

Respond with JSON containing:
- "tasks": Array of detailed task objects
- "fileStructure": Recommended file organization
- "technologyStackSuggestion": Recommended libraries and frameworks for game development

Ensure all tasks are actionable and lead to a complete, playable game.`;
  }
}
