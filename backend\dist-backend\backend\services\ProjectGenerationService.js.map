{"version": 3, "file": "ProjectGenerationService.js", "sourceRoot": "", "sources": ["../../../services/ProjectGenerationService.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AACpC,OAAO,EASL,SAAS,EAEV,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAWvE;;;GAGG;AACH,MAAM,OAAO,wBAAwB;IAOnC,YAAY,EAAkB;QALtB,kBAAa,GAAgC,IAAI,CAAC;QAClD,iBAAY,GAA+B,IAAI,CAAC;QAChD,iBAAY,GAAG,KAAK,CAAC;QACrB,qBAAgB,GAAkB,IAAI,CAAC;QAG7C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,MAAc;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAEtD,MAAM,gBAAgB,GAA4B;YAChD,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5C,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YACtC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;SACzD,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CACjC,SAAiB,EACjB,WAAmB,EACnB,WAAwB,EACxB,uBAAkD;QAElD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,oCAAoC,WAAW,EAAE,EAAE,SAAS,CAAC,CAAC;YAEjG,iCAAiC;YACjC,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,oCAAoC,EAAE,SAAS,CAAC,CAAC;YAE3F,MAAM,YAAY,GAAG,uBAAuB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAChE,MAAM,cAAc,GAAG,eAAe,SAAS,mBAAmB,WAAW,cAAc,WAAW,CAAC,IAAI,EAAE,CAAC;YAE9G,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CACtD,WAAW,EACX,YAAY,EACZ,cAAc,EACd,WAAW,CACZ,CAAC;YAEF,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,+BAA+B,WAAW,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,SAAS,CAAC,CAAC;YACtH,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,wBAAwB,EAAE,WAAW,WAAW,CAAC,KAAK,CAAC,MAAM,cAAc,WAAW,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAE1L,gCAAgC;YAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACxC,SAAS;gBACT,KAAK,EAAE,UAAU;gBACjB,IAAI,EAAE;oBACJ,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,yBAAyB,EAAE,WAAW,CAAC,yBAAyB;iBACjE;aACF,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,wCAAwC,EAAE,SAAS,CAAC,CAAC;YAE/F,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAC7D,WAAW,EACX,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,aAAa,IAAI,EAAE,EAC/B,cAAc,EACd,YAAY,EACZ,WAAW,CAAC,yBAAyB,CACtC,CAAC;YAEF,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,6BAA6B,EAAE,SAAS,CAAC,CAAC;YACpF,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,2BAA2B,EAAE,eAAe,WAAW,CAAC,KAAK,CAAC,MAAM,WAAW,WAAW,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE/K,+BAA+B;YAC/B,MAAM,UAAU,GAAW,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAwB,EAAE,EAAE,CAAC,CAAC;gBAC9E,GAAG,CAAC;gBACJ,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,MAAM,EAAE;gBACpB,MAAM,EAAE,SAAuB;gBAC/B,sBAAsB,EAAE,QAA+B;gBACvD,aAAa,EAAE,EAAE;gBACjB,cAAc,EAAE,EAAE;gBAClB,cAAc,EAAE,EAAE;gBAClB,eAAe,EAAE,CAAC;aACnB,CAAC,CAAC,CAAC;YAEJ,mCAAmC;YACnC,MAAM,kBAAkB,GAAe,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAiI,EAAE,EAAE,CAAC,CAAC;gBACnN,GAAG,CAAC;gBACJ,EAAE,EAAE,MAAM,EAAE;gBACZ,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAA+C,EAAE,EAAE,CAAC,CAAC;oBAC9E,GAAG,CAAC;oBACJ,EAAE,EAAE,MAAM,EAAE;oBACZ,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE;oBAC3B,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC,IAAI,EAAE;aACV,CAAC,CAAC,CAAC;YAEJ,8BAA8B;YAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACxC,SAAS;gBACT,KAAK,EAAE,gBAAgB;gBACvB,IAAI,EAAE;oBACJ,KAAK,EAAE,UAAU;oBACjB,aAAa,EAAE,kBAAkB;oBACjC,yBAAyB,EAAE,WAAW,CAAC,yBAAyB;oBAChE,WAAW,EAAE,WAAW,CAAC,WAAW;iBACrC;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,2CAA2C,EAAE,SAAS,CAAC,CAAC;QAE7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,8BAA8B,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YAE1F,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACvC,SAAS;gBACT,KAAK,EAAE,YAAY;aACpB,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAC9B,UAAkB,EAClB,cAAsB,EACtB,WAAmB,EACnB,aAAyB,EACzB,QAAsB,EACtB,SAAiB;QAEjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,6BAA6B,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAE9H,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,yBAAyB,CACxE,cAAc,EACd,WAAW,EACX,aAAa,EACb,QAAQ,EACR,SAAS,CACV,CAAC;QAEF,MAAM,QAAQ,GAAW,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAwB,EAAE,EAAE,CAAC,CAAC;YACjF,GAAG,CAAC;YACJ,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,MAAM,EAAE;YACpB,MAAM,EAAE,SAAuB;YAC/B,sBAAsB,EAAE,QAA+B;YACvD,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,CAAC;YAClB,OAAO,EAAE,sBAAsB;SAChC,CAAC,CAAC,CAAC;QAEJ,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,aAAa,QAAQ,CAAC,MAAM,2BAA2B,EAAE,SAAS,CAAC,CAAC;QAC9G,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,oCAAoC,EAAE,aAAa,QAAQ,CAAC,MAAM,iCAAiC,CAAC,CAAC;QAErJ,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,gBAAgB;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,KAAa,EAAE,OAAe,EAAE,MAAgD,EAAE,OAAgB;QAC5H,MAAM,QAAQ,GAAa;YACzB,EAAE,EAAE,MAAM,EAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK;YACL,OAAO;YACP,MAAM;SACP,CAAC;QAEF,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CACtB,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAgD,EAChD,KAA0B,EAC1B,WAA4E;QAE5E,MAAM,QAAQ,GAAG;YACf,MAAM;YACN,KAAK;YACL,OAAO;YACP,MAAM;YACN,KAAK;YACL,WAAW;YACX,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU,MAAM,KAAK,OAAO,EAAE,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,KAAa,EAAE,QAAgB,EAAE,SAAiB,EAAE,UAAmB,EAAE,MAAe;QACxH,yCAAyC;QACzC,IAAI,SAA2D,CAAC;QAChE,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,eAAe;gBAClB,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC;gBAC9B,MAAM;YACR,KAAK,aAAa;gBAChB,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;gBAC5B,MAAM;YACR,KAAK,uBAAuB;gBAC1B,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC;gBACtC,MAAM;YACR,KAAK,QAAQ;gBACX,SAAS,GAAG,QAAQ,CAAC;gBACrB,MAAM;YACR;gBACE,SAAS,GAAG,QAAQ,CAAC,CAAC,mBAAmB;QAC7C,CAAC;QAED,MAAM,KAAK,GAAqB;YAC9B,EAAE,EAAE,MAAM,EAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,UAAU;YAClB,MAAM;SACP,CAAC;QAEF,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,eAAe,QAAQ,MAAM,SAAS,EAAE,CAAC,CAAC;IACjE,CAAC;CACF"}